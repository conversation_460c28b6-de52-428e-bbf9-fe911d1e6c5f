{"name": "kaku", "scripts": {"dev": "npm run build-client && vite dev", "build": "npm run build-client && vite build", "deploy": "npm run build && npm run tailwind:build && wrangler deploy --minify", "tailwind:build": "tailwindcss -i src/main.css -o public/css/styles.css", "tailwind:watch": "tailwindcss -i src/main.css -o public/css/styles.css --watch", "cf:typegen": "wrangler types --env-interface Env", "test": "npm run test:node && npm run test:workers", "test:node": "vitest run --config vitest.unit.config.ts", "test:workers": "vitest run --config vitest.integration.config.ts", "test-form-generation": "node --import tsx test/scripts/measureLLMCallDuration.ts testllmFormGeneration", "test-parallel-llm-calls-performance": "node --import tsx test/scripts/measureLLMCallDuration.ts measureParallelCallsPerformance", "test-llm-prompt-rules": "node --import tsx test/scripts/llmPromptRules.ts", "test-llm-prompt-rules-test": "vitest run test/integration/llm-prompt-rules.test.ts", "test-image-comparisons-llm-calls": "node --import tsx test/scripts/measureLLMCallDuration.ts measureTwoImagesComparison", "test-detect-state-change-llm-call-duration": "node --import tsx test/scripts/measureLLMCallDuration.ts detectStateChangeLLMCallDuration", "test-detect-state-change-by-checking-page-state-result": "node --import tsx test/scripts/measureLLMCallDuration.ts detectChangeByComparingPageStateResult", "test:gemini:performance": "node --import tsx test/scripts/geminiPerformance.ts performance 5", "test:gemini:single": "node --import tsx test/scripts/geminiPerformance.ts single", "test:gemini:htmx": "node --import tsx test/scripts/htmxFormGeneratorTest.ts", "test:watch": "vitest", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\"", "build-client": "node scripts/build-client.mjs", "watch": "node scripts/watch-client.mjs"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google/genai": "^1.9.0", "@hyperbrowser/sdk": "^0.44.0", "@twind/core": "^1.1.3", "@twind/preset-tailwind": "^1.1.4", "agents": "0.0.111", "argon2-wasm-edge": "^1.0.23", "hono": "4.8.12", "hono-agents": "0.0.101", "openai": "^4.87.1", "pixelmatch": "^7.1.0", "tsx": "^4.19.4", "upng-js": "^2.1.0"}, "devDependencies": {"@cloudflare/vite-plugin": "1.9.4", "@cloudflare/vitest-pool-workers": "^0.8.53", "@tailwindcss/vite": "^4.1.11", "@types/jsdom": "^21.1.7", "@types/upng-js": "^2.1.5", "chokidar-cli": "^3.0.0", "devtools-protocol": "^0.0.1473885", "esbuild": "^0.19.0", "gpt-tokenizer": "^2.1.2", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "terser": "^5.30.4", "tslib": "^2.8.1", "vite": "7.0.4", "vitest": "3.2.4", "wrangler": "^4.26.0"}}